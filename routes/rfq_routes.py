"""RFQ Routes for Talaria Dashboard.

This module handles the web routes for RFQ (Request for Quotation) email automation.
Provides endpoints for form-based email sending and status monitoring.

Developed for Ligentec SA - RFQ Email Automation Feature
"""

import logging
import os
from io import BytesIO

from flask import (
    Blueprint,
    current_app,
    flash,
    jsonify,
    redirect,
    render_template,
    request,
    url_for,
)
from flask_mail import Mail

from core.auth.auth import check_permission, login_required
from core.services.rfq_email_service import RFQEmailService

# Configure logging
logger = logging.getLogger(__name__)

# Create blueprint
rfq_bp = Blueprint("rfq", __name__, url_prefix="/rfq")


def init_rfq_service():
    """Initialize RFQ Email Service with Flask-Mail instance."""
    mail = Mail(current_app)
    return RFQEmailService(mail)


@rfq_bp.route("/")
@login_required
def rfq_automation():
    """Display the RFQ automation page."""
    try:
        # Check permissions
        if not check_permission("view"):
            flash("You do not have permission to access RFQ automation.", "error")
            return redirect(url_for("home"))

        return render_template("rfq_automation.html")

    except Exception as e:
        logger.error(f"Error loading RFQ automation page: {str(e)}")
        flash("Error loading RFQ automation page.", "error")
        return redirect(url_for("home"))


@rfq_bp.route("/send", methods=["POST"])
@login_required
def send_form_rfq_email():
    """Send RFQ email based on form data (test mode)."""
    try:
        # Check permissions
        if not check_permission("modify"):
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "You do not have permission to send RFQ emails.",
                    }
                ),
                403,
            )

        # Get form data
        form_data = {}
        if request.is_json:
            form_data = request.get_json() or {}
        else:
            # Handle form data from regular form submission
            form_data = {
                "xfabCloudUrl": request.form.get("xfabCloudUrl", "").strip(),
                "xfabCloudUrl2": request.form.get("xfabCloudUrl2", "").strip(),
                "xfabCloudUrl3": request.form.get("xfabCloudUrl3", "").strip(),
                "xfabCloudUrl4": request.form.get("xfabCloudUrl4", "").strip(),
                "xfabCloudUrl5": request.form.get("xfabCloudUrl5", "").strip(),
            }

        # Validate required fields
        if not form_data.get("xfabCloudUrl"):
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "At least one SoW XFab Cloud URL is required.",
                    }
                ),
                400,
            )

        # Initialize RFQ service and send email
        rfq_service = init_rfq_service()
        results = rfq_service.send_form_based_rfq_email(
            form_data, pdf_file_path=None, test_mode=True
        )

        return jsonify(results)

    except Exception as e:
        logger.error(f"Error sending form-based RFQ email: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500


@rfq_bp.route("/send-production", methods=["POST"])
@login_required
def send_production_rfq_email():
    """Send RFQ email based on form data (production mode)."""
    try:
        # Check permissions
        if not check_permission("modify"):
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "You do not have permission to send RFQ emails.",
                    }
                ),
                403,
            )

        # Get form data
        form_data = {}
        if request.is_json:
            form_data = request.get_json() or {}
        else:
            # Handle form data from regular form submission
            form_data = {
                "xfabCloudUrl": request.form.get("xfabCloudUrl", "").strip(),
                "xfabCloudUrl2": request.form.get("xfabCloudUrl2", "").strip(),
                "xfabCloudUrl3": request.form.get("xfabCloudUrl3", "").strip(),
                "xfabCloudUrl4": request.form.get("xfabCloudUrl4", "").strip(),
                "xfabCloudUrl5": request.form.get("xfabCloudUrl5", "").strip(),
            }

        # Validate required fields
        if not form_data.get("xfabCloudUrl"):
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "At least one SoW XFab Cloud URL is required.",
                    }
                ),
                400,
            )

        # Get test mode from request
        test_mode = request.form.get("mode") == "test" if not request.is_json else form_data.get("test_mode", False)

        # Initialize RFQ service and send email
        rfq_service = init_rfq_service()
        results = rfq_service.send_form_based_rfq_email(
            form_data, pdf_file_path=None, test_mode=test_mode
        )

        return jsonify(results)

    except Exception as e:
        logger.error(f"Error sending production RFQ email: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500


@rfq_bp.route("/preview", methods=["POST"])
@login_required
def preview_email():
    """Generate email preview for editing."""
    try:
        # Check permissions
        if not check_permission("modify"):
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "You do not have permission to preview emails.",
                    }
                ),
                403,
            )

        # Get form data
        form_data = request.get_json()
        if not form_data:
            return jsonify({"success": False, "message": "No form data provided"}), 400

        # Initialize RFQ service
        rfq_service = init_rfq_service()

        # Generate preview
        preview_data = rfq_service.generate_email_preview(form_data)

        return jsonify({"success": True, "preview": preview_data})

    except Exception as e:
        current_app.logger.error(f"Error generating email preview: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500


@rfq_bp.route("/send-custom", methods=["POST"])
@login_required
def send_custom_email():
    """Send custom email with user-modified content."""
    try:
        # Check permissions
        if not check_permission("modify"):
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "You do not have permission to send emails.",
                    }
                ),
                403,
            )

        # Get email data
        email_data = request.get_json()
        if not email_data:
            return jsonify({"success": False, "message": "No email data provided"}), 400

        # Extract required fields
        subject = email_data.get("subject", "")
        body = email_data.get("body", "")
        recipients = email_data.get("recipients", {})
        test_mode = email_data.get("test_mode", True)

        if not subject or not body:
            return (
                jsonify({"success": False, "message": "Subject and body are required"}),
                400,
            )

        # Initialize RFQ service
        rfq_service = init_rfq_service()

        # Send custom email
        results = rfq_service.send_custom_email(
            subject=subject, body=body, recipients=recipients, test_mode=test_mode
        )

        return jsonify(results)

    except Exception as e:
        current_app.logger.error(f"Error sending custom email: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500


# Error handlers for the blueprint
@rfq_bp.errorhandler(404)
def not_found(error):
    """Handle 404 errors."""
    return jsonify({"success": False, "message": "Endpoint not found."}), 404


@rfq_bp.errorhandler(500)
def internal_error(error):
    """Handle 500 errors."""
    return jsonify({"success": False, "message": "Internal server error."}), 500
