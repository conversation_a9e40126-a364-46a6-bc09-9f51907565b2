{% extends "base.html" %} {% block title %}RFQ Email Automation - Talaria
Dashboard{% endblock %} {% block extra_css %}
<style>
  .rfq-form {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 12px;
    padding: 2rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .form-group {
    position: relative;
  }

  .form-group input:focus {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px -8px rgba(59, 130, 246, 0.3);
  }

  .pdf-upload-area {
    position: relative;
    overflow: hidden;
  }

  .pdf-upload-area:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px -8px rgba(59, 130, 246, 0.2);
  }

  .pdf-upload-area.dragover {
    border-color: #3b82f6;
    background-color: #dbeafe;
    transform: scale(1.02);
  }

  .dark .pdf-upload-area.dragover {
    background-color: rgba(59, 130, 246, 0.1);
  }

  /* Custom focus styles for better accessibility */
  .form-group input:focus,
  .pdf-upload-area:focus-within {
    outline: none;
    ring: 4px;
    ring-color: rgba(59, 130, 246, 0.2);
  }

  /* Smooth animations */
  .form-group input,
  .pdf-upload-area,
  button {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Button hover effects */
  button:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  /* Dark mode enhancements */
  .dark .rfq-form {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    border-color: #374151;
  }

  .mode-toggle {
    background-color: #fed7d7;
    border: 2px solid #fc8181;
    color: #c53030;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: bold;
  }

  .mode-toggle.production {
    background-color: #c6f6d5;
    border-color: #68d391;
    color: #2f855a;
  }
</style>
{% endblock %} {% block content %}
<!-- Hidden element to pass existing data to JavaScript -->
<script type="application/json" id="existing-data">
  {{ existing_data | tojson | safe if existing_data else '{}' }}
</script>

<div class="container mx-auto px-4 py-6">
  <!-- Header -->
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
      <i class="fas fa-envelope-open-text mr-3 text-blue-600"></i>
      RFQ Email Automation
    </h1>
    <p class="text-gray-600 dark:text-gray-300"></p>
  </div>

  <!-- Data Persistence Banner -->
  {% if existing_data %}
  <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6">
    <div class="flex">
      <div class="flex-shrink-0">
        <i class="fas fa-info-circle text-blue-400"></i>
      </div>
      <div class="ml-3">
        <p class="text-sm text-blue-700">
          <strong>Previous session restored!</strong> Your RFQ data persists
          while you're logged in.
          <br />
          <span class="text-xs"
            >You can navigate to other pages and return without losing your
            work. Use "Clear & Start Fresh" for new tasks.</span
          >
        </p>
      </div>
    </div>
  </div>
  {% endif %}

  <!-- RFQ Input Section -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
    <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
      <i class="fas fa-edit mr-2"></i>RFQ Information
    </h2>

    <!-- RFQ Form -->
    <div id="rfqForm" class="rfq-form">
      <form id="rfqDataForm" class="space-y-6">
        <!-- Row 1: Sifo Project and Talos Lot ID -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="form-group">
            <label
              for="sifoProject"
              class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2"
            >
              <i class="fas fa-project-diagram mr-2 text-blue-500"></i>Sifo
              Project
            </label>
            <input
              type="text"
              id="sifoProject"
              name="sifoProject"
              class="w-full px-4 py-3 border-2 border-gray-200 dark:border-gray-600 rounded-lg focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 transition-all duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
              placeholder="Ex: AN350_05-124_TNG2.1"
              required
            />
            <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
              Please enter the Sifo Project as mentioned in Asana.
            </div>
          </div>

          <div class="form-group">
            <label
              for="talosLotId"
              class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2"
            >
              <i class="fas fa-barcode mr-2 text-green-500"></i>Talos Lot ID
            </label>
            <input
              type="text"
              id="talosLotId"
              name="talosLotId"
              class="w-full px-4 py-3 border-2 border-gray-200 dark:border-gray-600 rounded-lg focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 transition-all duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
              placeholder="Ex: 6AADQ101M00"
              required
            />
            <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
              Please enter the Talos Lot ID as it appears in Asana.
            </div>
          </div>
        </div>

        <!-- Row 2: Lot Project -->
        <div class="form-group">
          <label
            for="lotProject"
            class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2"
          >
            <i class="fas fa-layer-group mr-2 text-orange-500"></i>Lot Project
          </label>
          <input
            type="text"
            id="lotProject"
            name="lotProject"
            class="w-full px-4 py-3 border-2 border-gray-200 dark:border-gray-600 rounded-lg focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 transition-all duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
            placeholder="Ex: AN800"
            required
          />
          <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
            Please Enter a valid Lot Project.
          </div>
        </div>

        <!-- XFab Cloud URLs Section -->
        <div class="form-group">
          <label
            class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3"
          >
            <i class="fas fa-cloud mr-2 text-cyan-500"></i>XFab Cloud URLs
            <span class="text-xs text-gray-500 ml-2">(Up to 5 URLs)</span>
          </label>

          <!-- URL 1 (Required) -->
          <div class="mb-4">
            <label
              for="xfabCloudUrl"
              class="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1"
            >
              URL 1 (Required)
            </label>
            <input
              type="url"
              id="xfabCloudUrl"
              name="xfabCloudUrl"
              class="w-full px-4 py-3 border-2 border-gray-200 dark:border-gray-600 rounded-lg focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 transition-all duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
              placeholder="https://cloud.xfab.com/index.php/s/..."
              required
            />
          </div>

          <!-- URL 2 (Optional) -->
          <div class="mb-4">
            <label
              for="xfabCloudUrl2"
              class="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1"
            >
              URL 2 (Optional)
            </label>
            <input
              type="url"
              id="xfabCloudUrl2"
              name="xfabCloudUrl2"
              class="w-full px-4 py-3 border-2 border-gray-200 dark:border-gray-600 rounded-lg focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 transition-all duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
              placeholder="https://cloud.xfab.com/index.php/s/..."
            />
          </div>

          <!-- URL 3 (Optional) -->
          <div class="mb-4">
            <label
              for="xfabCloudUrl3"
              class="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1"
            >
              URL 3 (Optional)
            </label>
            <input
              type="url"
              id="xfabCloudUrl3"
              name="xfabCloudUrl3"
              class="w-full px-4 py-3 border-2 border-gray-200 dark:border-gray-600 rounded-lg focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 transition-all duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
              placeholder="https://cloud.xfab.com/index.php/s/..."
            />
          </div>

          <!-- URL 4 (Optional) -->
          <div class="mb-4">
            <label
              for="xfabCloudUrl4"
              class="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1"
            >
              URL 4 (Optional)
            </label>
            <input
              type="url"
              id="xfabCloudUrl4"
              name="xfabCloudUrl4"
              class="w-full px-4 py-3 border-2 border-gray-200 dark:border-gray-600 rounded-lg focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 transition-all duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
              placeholder="https://cloud.xfab.com/index.php/s/..."
            />
          </div>

          <!-- URL 5 (Optional) -->
          <div class="mb-4">
            <label
              for="xfabCloudUrl5"
              class="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1"
            >
              URL 5 (Optional)
            </label>
            <input
              type="url"
              id="xfabCloudUrl5"
              name="xfabCloudUrl5"
              class="w-full px-4 py-3 border-2 border-gray-200 dark:border-gray-600 rounded-lg focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 transition-all duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
              placeholder="https://cloud.xfab.com/index.php/s/..."
            />
          </div>

          <div
            class="mt-2 p-3 bg-cyan-50 dark:bg-cyan-900/30 rounded-lg border border-cyan-200 dark:border-cyan-800"
          >
            <p class="text-xs text-cyan-700 dark:text-cyan-300">
              <i class="fas fa-info-circle mr-1"></i>
              <strong>Example:</strong>
              https://cloud.xfab.com/index.php/s/A3on9tSzbQ2ctkt?path=%2F1-Operations%2FRFQ_SoW_batch_ID%2FAN350_05-124_TNG2.1
            </p>
          </div>
        </div>

        <!-- PDF Attachment Section -->
        <div class="form-group">
          <label
            class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3"
          >
            <i class="fas fa-file-pdf mr-2 text-red-500"></i>PDF Attachment
            (Optional)
          </label>

          <div
            class="pdf-upload-area border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center hover:border-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 cursor-pointer"
            id="pdfUploadArea"
          >
            <div class="pdf-upload-content">
              <i
                class="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-3"
              ></i>
              <p
                class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-1"
              >
                Drop PDF file here or click to browse
              </p>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                Maximum file size: 25MB
              </p>
            </div>

            <input
              type="file"
              id="pdfFile"
              name="pdfFile"
              accept=".pdf"
              class="hidden"
            />
          </div>

          <!-- PDF File Info -->
          <div
            id="pdfFileInfo"
            class="hidden mt-4 p-4 bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 rounded-lg"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <i class="fas fa-file-pdf text-red-500 text-xl mr-3"></i>
                <div>
                  <p
                    class="font-medium text-green-700 dark:text-green-300"
                    id="pdfFileName"
                  ></p>
                  <p
                    class="text-sm text-green-600 dark:text-green-400"
                    id="pdfFileSize"
                  ></p>
                </div>
              </div>
              <button
                type="button"
                id="removePdfFile"
                class="text-red-500 hover:text-red-700 dark:hover:text-red-400 transition-colors duration-200"
              >
                <i class="fas fa-times text-lg"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div
          class="flex flex-col sm:flex-row gap-4 pt-4 border-t border-gray-200 dark:border-gray-600"
        >
          <button
            type="button"
            id="validateFormBtn"
            class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 focus:ring-4 focus:ring-blue-500/20"
            title="Optional: Manual validation (form validates automatically as you type)"
          >
            <i class="fas fa-check-circle mr-2"></i>Manual Validation
          </button>

          <button
            type="button"
            id="clearFormBtn"
            class="flex-1 sm:flex-none bg-gray-500 hover:bg-gray-600 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200"
          >
            <i class="fas fa-eraser mr-2"></i>Clear Form
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Email Sending Section -->
  <div
    id="sendSection"
    class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hidden"
  >
    <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
      <i class="fas fa-paper-plane mr-2"></i>Send RFQ Email
    </h2>

    <!-- Action Buttons -->
    <div class="flex space-x-4">
      <button
        type="button"
        id="sendAllBtn"
        class="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-3 rounded-lg transition-all duration-200 transform hover:scale-105 focus:ring-4 focus:ring-blue-500/20"
      >
        <i class="fas fa-paper-plane mr-2"></i>Send RFQ Email
      </button>
      <button
        type="button"
        id="clearSessionBtn"
        class="bg-gray-500 hover:bg-gray-600 text-white font-semibold px-6 py-3 rounded-lg transition-all duration-200"
        title="Clear current data and start fresh"
      >
        <i class="fas fa-refresh mr-2"></i>Clear Form
      </button>
    </div>
  </div>

  <!-- Results Section -->
  <div
    id="resultsSection"
    class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hidden"
  >
    <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
      <i class="fas fa-chart-bar mr-2"></i>Email Sending Results
    </h2>
    <div id="resultsContent">
      <!-- Results will be displayed here -->
    </div>
  </div>
</div>

<!-- Loading Modal -->
<div
  id="loadingModal"
  class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden"
>
  <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-sm w-full mx-4">
    <div class="text-center">
      <div
        class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"
      ></div>
      <p class="text-gray-700 dark:text-gray-300" id="loadingText">
        Processing...
      </p>
    </div>
  </div>
</div>
{% endblock %} {% block extra_js %}
<!-- Include RFQ-specific JavaScript -->
<script src="{{ url_for('static', filename='js/rfq.js') }}"></script>
{% endblock %}
