{% extends "base.html" %} {% block title %}RFQ Email Automation - Talaria Dashboard{% endblock %} 

{% block extra_css %}
<style>
  /* Professional RFQ Template Styling */
  .rfq-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: calc(100vh - 4rem);
    padding: 2rem 0;
  }

  .rfq-main-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 24px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    overflow: hidden;
  }

  .dark .rfq-main-card {
    background: rgba(31, 41, 55, 0.95);
    border-color: rgba(75, 85, 99, 0.2);
  }

  .rfq-header {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    padding: 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
  }

  .rfq-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: repeating-linear-gradient(
      45deg,
      transparent,
      transparent 2px,
      rgba(255, 255, 255, 0.03) 2px,
      rgba(255, 255, 255, 0.03) 4px
    );
    animation: subtle-move 20s linear infinite;
  }

  @keyframes subtle-move {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
  }

  .rfq-header-content {
    position: relative;
    z-index: 1;
  }

  .rfq-form {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 20px;
    padding: 2.5rem;
    margin: 2rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
    position: relative;
  }

  .dark .rfq-form {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    border-color: #374151;
  }

  .section-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, #e5e7eb, transparent);
    margin: 2rem 0;
  }

  .dark .section-divider {
    background: linear-gradient(90deg, transparent, #374151, transparent);
  }

  .form-group {
    position: relative;
    margin-bottom: 1.5rem;
  }

  .form-group-header {
    background: linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%);
    border: 1px solid #c7d2fe;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
  }

  .dark .form-group-header {
    background: linear-gradient(135deg, #312e81 0%, #1e1b4b 100%);
    border-color: #4338ca;
  }

  .sow-section {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 1px solid #bae6fd;
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
  }

  .sow-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px -8px rgba(59, 130, 246, 0.3);
  }

  .dark .sow-section {
    background: linear-gradient(135deg, #164e63 0%, #0c4a6e 100%);
    border-color: #0284c7;
  }

  .sow-required {
    border-left: 4px solid #059669;
  }

  .sow-optional {
    border-left: 4px solid #3b82f6;
  }

  .form-group input:focus, .form-group select:focus {
    transform: translateY(-1px);
    box-shadow: 0 10px 30px -10px rgba(59, 130, 246, 0.4);
    outline: none;
    border-color: #3b82f6;
    ring: 3px;
    ring-color: rgba(59, 130, 246, 0.2);
  }

  .professional-input {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 0.875rem 1rem;
    font-size: 0.95rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .professional-input:focus {
    background: rgba(255, 255, 255, 1);
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .dark .professional-input {
    background: rgba(55, 65, 81, 0.9);
    border-color: #4b5563;
    color: #f9fafb;
  }

  .dark .professional-input:focus {
    background: rgba(55, 65, 81, 1);
    border-color: #3b82f6;
  }

  .btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: none;
    color: white;
    font-weight: 600;
    padding: 1rem 2rem;
    border-radius: 12px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px -10px rgba(59, 130, 246, 0.5);
  }

  .btn-primary:active {
    transform: translateY(0);
  }

  .btn-success {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    border: none;
    color: white;
    font-weight: 600;
    padding: 1rem 2rem;
    border-radius: 12px;
    transition: all 0.3s ease;
  }

  .btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px -10px rgba(5, 150, 105, 0.5);
  }

  .btn-secondary {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    border: none;
    color: white;
    font-weight: 600;
    padding: 1rem 2rem;
    border-radius: 12px;
    transition: all 0.3s ease;
  }

  .btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px -10px rgba(107, 114, 128, 0.5);
  }

  .info-panel {
    background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
    border: 1px solid #fbbf24;
    border-left: 4px solid #f59e0b;
    border-radius: 12px;
    padding: 1rem;
    margin: 1rem 0;
  }

  .dark .info-panel {
    background: linear-gradient(135deg, #78350f 0%, #92400e 100%);
    border-color: #d97706;
  }

  .persistence-banner {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    border: 1px solid #60a5fa;
    border-left: 4px solid #3b82f6;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .dark .persistence-banner {
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
    border-color: #3b82f6;
  }

  .loading-overlay {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
  }

  .dark .loading-overlay {
    background: rgba(31, 41, 55, 0.95);
  }

  .modal-content {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .dark .modal-content {
    background: rgba(31, 41, 55, 0.98);
    border-color: rgba(75, 85, 99, 0.2);
  }

  /* Icon styling */
  .icon-primary { color: #3b82f6; }
  .icon-success { color: #059669; }
  .icon-warning { color: #f59e0b; }
  .icon-info { color: #06b6d4; }

  /* Smooth animations */
  * {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Responsive design improvements */
  @media (max-width: 768px) {
    .rfq-form {
      margin: 1rem;
      padding: 1.5rem;
    }
    
    .rfq-header {
      padding: 1.5rem;
    }
  }
</style>
{% endblock %} {% block content %}
<!-- Hidden element to pass existing data to JavaScript -->
<script type="application/json" id="existing-data">
  {{ existing_data | tojson | safe if existing_data else '{}' }}
</script>

<div class="rfq-container">
  <div class="container mx-auto px-4">
    <!-- Professional Main Card -->
    <div class="rfq-main-card max-w-6xl mx-auto">
      <!-- Enhanced Header -->
      <div class="rfq-header">
        <div class="rfq-header-content">
          <h1 class="text-4xl font-bold mb-4">
            <i class="fas fa-envelope-open-text mr-4 icon-info"></i>
            RFQ Email Automation
          </h1>
          <p class="text-lg opacity-90 max-w-2xl mx-auto">
            Professional RFQ email generation and distribution system with intelligent automation and comprehensive project management capabilities.
          </p>
        </div>
      </div>

      <!-- Data Persistence Banner -->
      {% if existing_data %}
      <div class="persistence-banner mx-6 mt-6">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <i class="fas fa-info-circle icon-primary text-xl"></i>
          </div>
          <div class="ml-4">
            <h3 class="font-semibold text-blue-800 dark:text-blue-200 mb-2">
              Previous Session Restored!
            </h3>
            <p class="text-sm text-blue-700 dark:text-blue-300">
              Your RFQ data persists while you're logged in. Navigate freely between pages without losing your work.
            </p>
            <p class="text-xs text-blue-600 dark:text-blue-400 mt-1">
              Use "Clear & Start Fresh" to begin new tasks.
            </p>
          </div>
        </div>
      </div>
      {% endif %}

      <!-- RFQ Input Section -->
      <div class="rfq-form">
        <div class="form-group-header">
          <h2 class="text-2xl font-bold text-indigo-900 dark:text-indigo-100 mb-2">
            <i class="fas fa-edit mr-3 icon-primary"></i>RFQ Information & Project Details
          </h2>
          <p class="text-sm text-indigo-700 dark:text-indigo-300">
            Configure your Statement of Work (SoW) documents and XFab Cloud URLs for professional RFQ generation
          </p>
        </div>

        <!-- RFQ Form -->
        <form id="rfqDataForm" class="space-y-8">
          <!-- XFab Cloud URLs Section -->
          <div class="form-group">
            <div class="form-group-header">
              <h3 class="text-lg font-semibold text-indigo-800 dark:text-indigo-200 mb-2">
                <i class="fas fa-cloud mr-2 icon-info"></i>Statement of Work (SoW) Configuration
              </h3>
              <p class="text-sm text-indigo-600 dark:text-indigo-400">
                Add up to 5 SoW projects with their corresponding XFab Cloud URLs for comprehensive project documentation
              </p>
            </div>

            <!-- SoW 1 (Required) -->
            <div class="sow-section sow-required">
              <div class="flex items-center mb-3">
                <i class="fas fa-file-contract icon-success mr-2"></i>
                <h4 class="text-base font-semibold text-green-800 dark:text-green-200">
                  SoW 1 (Required)
                </h4>
                <span class="ml-auto px-3 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs font-medium rounded-full">
                  Mandatory
                </span>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label for="sowTitle1" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <i class="fas fa-tag mr-1 icon-primary"></i>Project Title
                  </label>
                  <input
                    type="text"
                    id="sowTitle1"
                    name="sowTitle1"
                    class="professional-input w-full"
                    placeholder="e.g., HAD2 LOT 4 6AACZ206M00"
                  />
                </div>
                <div>
                  <label for="xfabCloudUrl" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <i class="fas fa-cloud mr-1 icon-info"></i>XFab Cloud URL
                  </label>
                  <input
                    type="url"
                    id="xfabCloudUrl"
                    name="xfabCloudUrl"
                    class="professional-input w-full"
                    placeholder="https://cloud.xfab.com/index.php/s/..."
                    required
                  />
                </div>
              </div>
            </div>

            <!-- SoW 2 (Optional) -->
            <div class="sow-section sow-optional">
              <div class="flex items-center mb-3">
                <i class="fas fa-file-alt icon-primary mr-2"></i>
                <h4 class="text-base font-semibold text-blue-800 dark:text-blue-200">
                  SoW 2 (Optional)
                </h4>
                <span class="ml-auto px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs font-medium rounded-full">
                  Optional
                </span>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label for="sowTitle2" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <i class="fas fa-tag mr-1 icon-primary"></i>Project Title
                  </label>
                  <input
                    type="text"
                    id="sowTitle2"
                    name="sowTitle2"
                    class="professional-input w-full"
                    placeholder="e.g., HAD2 LOT 5 6AACZ207M00"
                  />
                </div>
                <div>
                  <label for="xfabCloudUrl2" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <i class="fas fa-cloud mr-1 icon-info"></i>XFab Cloud URL
                  </label>
                  <input
                    type="url"
                    id="xfabCloudUrl2"
                    name="xfabCloudUrl2"
                    class="professional-input w-full"
                    placeholder="https://cloud.xfab.com/index.php/s/..."
                  />
                </div>
              </div>
            </div>

            <!-- SoW 3 (Optional) -->
            <div class="sow-section sow-optional">
              <div class="flex items-center mb-3">
                <i class="fas fa-file-alt icon-primary mr-2"></i>
                <h4 class="text-base font-semibold text-blue-800 dark:text-blue-200">
                  SoW 3 (Optional)
                </h4>
                <span class="ml-auto px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs font-medium rounded-full">
                  Optional
                </span>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label for="sowTitle3" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <i class="fas fa-tag mr-1 icon-primary"></i>Project Title
                  </label>
                  <input
                    type="text"
                    id="sowTitle3"
                    name="sowTitle3"
                    class="professional-input w-full"
                    placeholder="e.g., HAD2 LOT 6 6AACZ208M00"
                  />
                </div>
                <div>
                  <label for="xfabCloudUrl3" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <i class="fas fa-cloud mr-1 icon-info"></i>XFab Cloud URL
                  </label>
                  <input
                    type="url"
                    id="xfabCloudUrl3"
                    name="xfabCloudUrl3"
                    class="professional-input w-full"
                    placeholder="https://cloud.xfab.com/index.php/s/..."
                  />
                </div>
              </div>
            </div>

            <!-- SoW 4 (Optional) -->
            <div class="sow-section sow-optional">
              <div class="flex items-center mb-3">
                <i class="fas fa-file-alt icon-primary mr-2"></i>
                <h4 class="text-base font-semibold text-blue-800 dark:text-blue-200">
                  SoW 4 (Optional)
                </h4>
                <span class="ml-auto px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs font-medium rounded-full">
                  Optional
                </span>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label for="sowTitle4" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <i class="fas fa-tag mr-1 icon-primary"></i>Project Title
                  </label>
                  <input
                    type="text"
                    id="sowTitle4"
                    name="sowTitle4"
                    class="professional-input w-full"
                    placeholder="e.g., HAD2 LOT 7 6AACZ209M00"
                  />
                </div>
                <div>
                  <label for="xfabCloudUrl4" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <i class="fas fa-cloud mr-1 icon-info"></i>XFab Cloud URL
                  </label>
                  <input
                    type="url"
                    id="xfabCloudUrl4"
                    name="xfabCloudUrl4"
                    class="professional-input w-full"
                    placeholder="https://cloud.xfab.com/index.php/s/..."
                  />
                </div>
              </div>
            </div>

            <!-- SoW 5 (Optional) -->
            <div class="sow-section sow-optional">
              <div class="flex items-center mb-3">
                <i class="fas fa-file-alt icon-primary mr-2"></i>
                <h4 class="text-base font-semibold text-blue-800 dark:text-blue-200">
                  SoW 5 (Optional)
                </h4>
                <span class="ml-auto px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs font-medium rounded-full">
                  Optional
                </span>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label for="sowTitle5" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <i class="fas fa-tag mr-1 icon-primary"></i>Project Title
                  </label>
                  <input
                    type="text"
                    id="sowTitle5"
                    name="sowTitle5"
                    class="professional-input w-full"
                    placeholder="e.g., HAD2 LOT 8 6AACZ210M00"
                  />
                </div>
                <div>
                  <label for="xfabCloudUrl5" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <i class="fas fa-cloud mr-1 icon-info"></i>XFab Cloud URL
                  </label>
                  <input
                    type="url"
                    id="xfabCloudUrl5"
                    name="xfabCloudUrl5"
                    class="professional-input w-full"
                    placeholder="https://cloud.xfab.com/index.php/s/..."
                  />
                </div>
              </div>
            </div>

            <!-- URL Example Info Panel -->
            <div class="info-panel">
              <div class="flex items-start">
                <i class="fas fa-lightbulb icon-warning mr-3 mt-1"></i>
                <div>
                  <h5 class="font-semibold text-amber-800 dark:text-amber-200 mb-1">Example URL Format</h5>
                  <p class="text-sm text-amber-700 dark:text-amber-300 break-all">
                    https://cloud.xfab.com/index.php/s/A3on9tSzbQ2ctkt?path=%2F1-Operations%2FRFQ_SoW_batch_ID%2FAN350_05-124_TNG2.1
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div class="section-divider"></div>

          <!-- Form Actions -->
          <div class="flex flex-col sm:flex-row gap-4">
            <button
              type="button"
              id="previewEmailBtn"
              class="flex-1 btn-success"
            >
              <i class="fas fa-eye mr-2"></i>Preview & Edit Email
            </button>

            <button
              type="button"
              id="validateFormBtn"
              class="flex-1 btn-primary"
              title="Optional: Manual validation (form validates automatically as you type)"
            >
              <i class="fas fa-check-circle mr-2"></i>Manual Validation
            </button>

            <button
              type="button"
              id="clearFormBtn"
              class="flex-1 sm:flex-none btn-secondary"
            >
              <i class="fas fa-eraser mr-2"></i>Clear Form
            </button>
          </div>
        </form>
      </div>

      <!-- Email Sending Section -->
      <div id="sendSection" class="rfq-form hidden">
        <div class="form-group-header">
          <h2 class="text-2xl font-bold text-indigo-900 dark:text-indigo-100 mb-2">
            <i class="fas fa-paper-plane mr-3 icon-success"></i>Send RFQ Email
          </h2>
          <p class="text-sm text-indigo-700 dark:text-indigo-300">
            Deploy your professionally crafted RFQ email to stakeholders and project teams
          </p>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4">
          <button type="button" id="sendAllBtn" class="flex-1 btn-primary">
            <i class="fas fa-paper-plane mr-2"></i>Send RFQ Email
          </button>
          <button
            type="button"
            id="clearSessionBtn"
            class="flex-1 sm:flex-none btn-secondary"
            title="Clear current data and start fresh"
          >
            <i class="fas fa-refresh mr-2"></i>Clear & Start Fresh
          </button>
        </div>
      </div>

      <!-- Results Section -->
      <div id="resultsSection" class="rfq-form hidden">
        <div class="form-group-header">
          <h2 class="text-2xl font-bold text-indigo-900 dark:text-indigo-100 mb-2">
            <i class="fas fa-chart-bar mr-3 icon-info"></i>Email Sending Results
          </h2>
          <p class="text-sm text-indigo-700 dark:text-indigo-300">
            Comprehensive delivery status and analytics for your RFQ email campaign
          </p>
        </div>
        <div id="resultsContent" class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <!-- Results will be displayed here -->
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Loading Modal -->
<div
  id="loadingModal"
  class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden loading-overlay"
>
  <div class="modal-content p-8 max-w-sm w-full mx-4">
    <div class="text-center">
      <div class="relative">
        <div class="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto mb-6"></div>
        <div class="absolute inset-0 rounded-full bg-gradient-to-r from-blue-400 to-purple-500 opacity-20 animate-pulse"></div>
      </div>
      <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Processing Request</h3>
      <p class="text-gray-600 dark:text-gray-400" id="loadingText">
        Please wait while we process your RFQ...
      </p>
    </div>
  </div>
</div>

<!-- Email Preview Modal -->
<div
  id="emailPreviewModal"
  class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden loading-overlay"
>
  <div class="flex items-center justify-center min-h-screen p-4">
    <div class="modal-content w-full max-w-4xl max-h-[90vh] flex flex-col">
      <!-- Modal Header -->
      <div class="bg-gradient-to-r from-green-600 to-green-700 text-white p-6 flex-shrink-0">
        <div class="flex justify-between items-center">
          <h3 class="text-2xl font-bold">
            <i class="fas fa-eye mr-3"></i>
            Email Preview & Advanced Editor
          </h3>
          <button
            id="closePreviewModal"
            class="text-white hover:text-green-200 transition-colors"
          >
            <i class="fas fa-times text-2xl"></i>
          </button>
        </div>
        <p class="text-green-100 mt-2 text-sm">
          Review and customize your RFQ email before sending to stakeholders
        </p>
      </div>

      <!-- Modal Body - Scrollable Content -->
      <div class="p-6 overflow-y-auto flex-1">
        <!-- Email Details -->
        <div class="space-y-6 mb-8">
          <!-- Subject Line -->
          <div class="form-group">
            <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
              <i class="fas fa-edit mr-2 icon-primary"></i>Subject Line (Fully Editable)
            </label>
            <input
              type="text"
              id="previewSubject"
              class="professional-input w-full"
              placeholder="Edit the email subject line..."
            />
          </div>

          <!-- Recipients -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="form-group">
              <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                <i class="fas fa-users mr-2 icon-primary"></i>To Recipients (Editable)
              </label>
              <input
                type="text"
                id="previewTo"
                class="professional-input w-full"
                placeholder="Edit recipients (comma-separated)..."
              />
            </div>
            <div class="form-group">
              <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                <i class="fas fa-user-plus mr-2 icon-info"></i>CC Recipients (Editable)
              </label>
              <input
                type="text"
                id="previewCC"
                class="professional-input w-full"
                placeholder="Edit CC recipients (comma-separated)..."
              />
            </div>
          </div>
        </div>

        <!-- Email Body Editor -->
        <div class="form-group mb-6">
          <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
            <i class="fas fa-edit mr-2 icon-primary"></i>Email Body (Rich Text Editor)
          </label>
          <div class="border-2 border-gray-200 dark:border-gray-600 rounded-xl overflow-hidden">
            <div
              id="emailBodyEditor"
              class="min-h-[300px] max-h-[400px] p-6 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 overflow-y-auto"
              contenteditable="true"
              style="line-height: 1.8; font-size: 15px;"
            >
              <!-- Email body content will be inserted here -->
            </div>
          </div>
          <div class="info-panel mt-4">
            <div class="flex items-start">
              <i class="fas fa-magic icon-warning mr-3 mt-1"></i>
              <div>
                <h5 class="font-semibold text-amber-800 dark:text-amber-200 mb-1">Maximum Editing Flexibility</h5>
                <p class="text-sm text-amber-700 dark:text-amber-300">
                  Edit any part of the email including subject, recipients, body content, links, and signature. All changes will be reflected in the final email sent to recipients.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Fixed Footer with Action Buttons -->
      <div class="border-t border-gray-200 dark:border-gray-600 p-6 bg-gray-50 dark:bg-gray-800 flex-shrink-0">
        <div class="flex flex-col sm:flex-row gap-4">
          <button
            type="button"
            id="refreshPreviewBtn"
            class="flex-1 btn-primary"
          >
            <i class="fas fa-sync-alt mr-2"></i>Refresh Preview
          </button>

          <button
            type="button"
            id="sendFromPreviewBtn"
            class="flex-1 btn-success"
          >
            <i class="fas fa-paper-plane mr-2"></i>Send Email
          </button>

          <button
            type="button"
            id="cancelPreviewBtn"
            class="flex-1 sm:flex-none btn-secondary"
          >
            <i class="fas fa-times mr-2"></i>Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block extra_js %}
<!-- Include RFQ-specific JavaScript -->
<script src="{{ url_for('static', filename='js/rfq.js') }}"></script>
{% endblock %}
